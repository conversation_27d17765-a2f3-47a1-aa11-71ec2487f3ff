apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: 'scs-web'
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-prod
    #kubernetes.io/ingress.class: "nginx"
    #nginx.ingress.kubernetes.io/affinity: "cookie"
    #nginx.ingress.kubernetes.io/session-cookie-name: "cc3-inventory-cookie"
    #nginx.ingress.kubernetes.io/session-cookie-expires: "172800"
    #nginx.ingress.kubernetes.io/session-cookie-max-age: "172800"
    #nginx.ingress.kubernetes.io/ssl-redirect: "false"
    #nginx.ingress.kubernetes.io/affinity-mode: persistent
    #nginx.ingress.kubernetes.io/session-cookie-hash: sha1

spec:
  ingressClassName: traefik
  tls:
  - hosts:
    - scs-web.coursity.io.vn
    secretName: scs-web-tls
  rules:
    - host: scs-web.coursity.io.vn
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: scs-web
                port:
                  number: 80