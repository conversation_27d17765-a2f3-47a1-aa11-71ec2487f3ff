apiVersion: apps/v1
kind: Deployment
metadata:
  name: wordpress
  namespace: wordpress
spec:
  selector:
    matchLabels:
      app: wordpress
  template:
    metadata:
      labels:
        app: wordpress
    spec:
      containers:
        - name: wordpress
          image: wordpress:6.8.3-php8.2-apache
          env:
            - name: WORDPRESS_DB_HOST
              value: mariadb
            - name: WOR<PERSON><PERSON>SS_DB_NAME
              value: wordpress
            - name: WORDPRESS_DB_USER
              value: wpuser
            - name: WORDPRESS_DB_PASSWORD
              value: wppass
          ports:
            - containerPort: 80
          volumeMounts:
            - name: wordpress-storage
              mountPath: /var/www/html
            - name: php-ini-config
              mountPath: /usr/local/etc/php/conf.d/uploads.ini
              subPath: uploads.ini
      volumes:
        - name: wordpress-storage
          persistentVolumeClaim:
            claimName: wordpress-pvc
        - name: php-ini-config
          configMap:
            name: wordpress-uploads-ini
            items:
              - key: uploads.ini
                path: uploads.ini
---
apiVersion: v1
kind: Service
metadata:
  name: wordpress
  namespace: wordpress
spec:
  type: ClusterIP
  ports:
    - name: http
      protocol: TCP
      port: 80
      targetPort: 80
  selector:
    app: wordpress
