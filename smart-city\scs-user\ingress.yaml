apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: 'scs-user'
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-prod
    #kubernetes.io/ingress.class: "nginx"
    #nginx.ingress.kubernetes.io/affinity: "cookie"
    #nginx.ingress.kubernetes.io/session-cookie-name: "cc3-inventory-cookie"
    #nginx.ingress.kubernetes.io/session-cookie-expires: "172800"
    #nginx.ingress.kubernetes.io/session-cookie-max-age: "172800"
    #nginx.ingress.kubernetes.io/ssl-redirect: "false"
    #nginx.ingress.kubernetes.io/affinity-mode: persistent
    #nginx.ingress.kubernetes.io/session-cookie-hash: sha1

spec:
  ingressClassName: traefik
  tls:
  - hosts:
    - scs-user.coursity.io.vn
    secretName: scs-user-tls
  rules:
    - host: scs-user.coursity.io.vn
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: scs-user
                port:
                  number: 80

