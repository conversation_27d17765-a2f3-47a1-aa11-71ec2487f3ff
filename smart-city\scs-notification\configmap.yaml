apiVersion: v1
kind: ConfigMap
metadata:
  name: scs-notification-config
  namespace: smart-city
data:
  NODE_ENV: "development"
  APP_PORT: "3000"
  APP_VERSION: "1"
  DB_HOST: "postgres"
  DB_PORT: "5432"
  DB_USER: "scs_user"
  DB_PASSWORD: "scs_user123!"
  DB_NAME: "smart_city"
  IGNORED_ROUTES: "/api/v1/health"
  KAFKA_BROKERS: "kafka-headless.default.svc.cluster.local:9092"
  KAFKA_CLIENT_ID: "scs-notification"
  KAFKA_GROUP_ID: "scs-notification"
  RESEND_API_KEY: "re_79TgbgE8_58jxifqTMoL2f2MKWANtCKkR"
