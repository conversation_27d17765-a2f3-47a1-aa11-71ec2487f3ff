apiVersion: apps/v1
kind: Deployment
metadata:
  name: coursity-fe
  annotations:
    configmap.reloader.stakater.com/reload: "coursity-fe-config"
spec:
  replicas: 1
  selector:
    matchLabels:
      app: coursity-fe
  template:
    metadata:
      labels:
        app: coursity-fe
    spec: 
      containers:
        - name: coursity-fe
          image: erikbuidt/coursity-fe:latest
          imagePullPolicy: 'Always'
          ports:
            - containerPort: 3000
          envFrom:
            - configMapRef:
                name: coursity-fe-config
