apiVersion: apps/v1
kind: Deployment
metadata:
  name: scs-web
  annotations:
    configmap.reloader.stakater.com/reload: "scs-web-config"
spec:
  replicas: 1
  selector:
    matchLabels:
      app: scs-web
  template:
    metadata:
      labels:
        app: scs-web
    spec: 
      containers:
        - name: scs-web
          image: erik1303/scss:scs-web
          imagePullPolicy: 'Always'
          ports:
            - containerPort: 3000
          envFrom:
            - configMapRef:
                name: scs-web-config
