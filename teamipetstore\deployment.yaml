apiVersion: apps/v1
kind: Deployment
metadata:
  name: teamipetstore

spec:
  replicas: 1
  selector:
    matchLabels:
      app: teamipetstore
  template:
    metadata:
      labels:
        app: teamipetstore
    spec: 
      containers:
        - name: teamipetstore
          image: erikbuidt/teamipetstore:latest
          imagePullPolicy: 'Always'
          ports:
            - containerPort: 3000

