apiVersion: apps/v1
kind: Deployment
metadata:
  name: cron-job-public-ip
spec:
  replicas: 1
  selector:
    matchLabels:
      app: cron-job-public-ip
  template:
    metadata:
      labels:
        app: cron-job-public-ip
    spec:
      containers:
        - name: cron-job-public-ip
          image: erikbuidt/cron-job-public-ip:latest
          imagePullPolicy: 'Always'
          ports:
            - containerPort: 3000
          env:
            - name: CLOUDFLARE_DNS_EDIT_TOKEN
              value: "****************************************"
            - name: CLOUDFLARE_ZONES
              value: "coursity.io.vn,teamipetstore.com"
            - name: CLOUDFLARE_DOMAINS_EDIT
              value: ""
          
          
