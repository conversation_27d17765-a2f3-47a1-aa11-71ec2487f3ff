apiVersion: v1
kind: ConfigMap
metadata:
  name: scs-user-config
  namespace: smart-city
data:
  EVN: "production"
  PORT: "1323"
  READ_TIMEOUT: "5s"
  WRITE_TIMEOUT: "5s"
  DB_HOST: "postgres"
  DB_PORT: "5432"
  DB_USER: "scs_user"
  DB_PASSWORD: "scs_user123!"
  DB_NAME: "smart_city"
  IGNORED_ROUTES: "/api/v1/health"
  KAFKA_BROKERS: "kafka-headless.default.svc.cluster.local:9092"
  LOG_LEVEL: "debug"
