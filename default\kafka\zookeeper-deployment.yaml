apiVersion: apps/v1
kind: Deployment
metadata:
  name: zookeeper
spec:
  replicas: 1
  selector:
    matchLabels:
      app: zookeeper
  template:
    metadata:
      labels:
        app: zookeeper
    spec:
      containers:
        - name: zookeeper
          image: confluentinc/cp-zookeeper:7.5.0
          ports:
            - containerPort: 2181
          env:
            - name: <PERSON><PERSON><PERSON><PERSON><PERSON>ER_CLIENT_PORT
              value: "2181"
            - name: <PERSON><PERSON><PERSON><PERSON>PER_TICK_TIME
              value: "2000"
            - name: Z<PERSON><PERSON>EEPER_DATA_DIR
              value: "/var/lib/zookeeper/data"
            - name: ZOOKEEPER_LOG_DIR
              value: "/var/lib/zookeeper/log"
            - name: <PERSON><PERSON><PERSON><PERSON><PERSON>ER_SERVER_ID
              value: "1"
          volumeMounts:
            - name: zookeeper-storage
              mountPath: /var/lib/zookeeper/data
      volumes:
        - name: zookeeper-storage
          persistentVolumeClaim:
            claimName: zookeeper-pvc
