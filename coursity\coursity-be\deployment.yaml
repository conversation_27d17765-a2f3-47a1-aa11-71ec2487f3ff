apiVersion: apps/v1
kind: Deployment
metadata:
  name: coursity-be
  annotations:
    configmap.reloader.stakater.com/reload: "coursity-be-config"
spec:
  replicas: 1
  selector:
    matchLabels:
      app: coursity-be
  template:
    metadata:
      labels:
        app: coursity-be
    spec:
      containers:
        - name: coursity-be
          image: erikbuidt/coursity-be:latest
          imagePullPolicy: 'Always'
          ports:
            - containerPort: 4000
          envFrom:
            - configMapRef:
                name: coursity-be-config

