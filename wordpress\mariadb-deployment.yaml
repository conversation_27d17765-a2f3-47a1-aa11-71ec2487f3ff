apiVersion: apps/v1
kind: Deployment
metadata:
  name: mariadb
  namespace: wordpress
spec:
  selector:
    matchLabels:
      app: mariadb
  template:
    metadata:
      labels:
        app: mariadb
    spec:
      containers:
        - name: mariadb
          image: mariadb:12.1.1-ubi10-rc
          env:
            - name: MARIADB_ROOT_PASSWORD
              value: rootpass
            - name: MARIADB_DATABASE
              value: wordpress
            - name: MARIADB_USER
              value: wpuser
            - name: MARIADB_PASSWORD
              value: wppass
          volumeMounts:
            - name: mariadb-storage
              mountPath: /bitnami/mariadb
      volumes:
        - name: mariadb-storage
          persistentVolumeClaim:
            claimName: mariadb-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: mariadb
  namespace: wordpress
spec:
  ports:
    - port: 3306
  selector:
    app: mariadb
