apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: 'scs-notification'
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-prod

spec:
  ingressClassName: traefik
  tls:
  - hosts:
    - scs-notification.coursity.io.vn
    secretName: scs-notification-tls
  rules:
    - host: scs-notification.coursity.io.vn
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: scs-notification
                port:
                  number: 80