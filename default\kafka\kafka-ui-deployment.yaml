apiVersion: apps/v1
kind: Deployment
metadata:
  name: kafka-ui
spec:
  replicas: 1
  selector:
    matchLabels:
      app: kafka-ui
  template:
    metadata:
      labels:
        app: kafka-ui
    spec:
      containers:
        - name: kafka-ui
          image: provectuslabs/kafka-ui:latest
          ports:
            - containerPort: 8080
          env:
            - name: KAFKA_CLUSTERS_0_NAME
              value: local-kafka
            - name: KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS
              value: kafka-headless:9092
            - name: KAFKA_CLUSTERS_0_ZOOKEEPER
              value: zookeeper:2181
