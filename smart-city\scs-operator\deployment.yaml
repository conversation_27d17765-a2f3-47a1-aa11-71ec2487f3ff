apiVersion: apps/v1
kind: Deployment
metadata:
  name: scs-operator
  annotations:
    configmap.reloader.stakater.com/reload: "scs-operator-config"
spec:
  replicas: 1
  selector:
    matchLabels:
      app: scs-operator
  template:
    metadata:
      labels:
        app: scs-operator
    spec:
      containers:
        - name: scs-operator
          image: erik1303/scss:scs-operator
          imagePullPolicy: 'Always'
          ports:
            - containerPort: 1323
          envFrom:
            - configMapRef:
                name: scs-operator-config

