apiVersion: apps/v1
kind: Deployment
metadata:
  name: scs-user
  annotations:
    configmap.reloader.stakater.com/reload: "scs-user-config"
spec:
  replicas: 1
  selector:
    matchLabels:
      app: scs-user
  template:
    metadata:
      labels:
        app: scs-user
    spec:
      containers:
        - name: scs-user
          image: erik1303/scss:scs-user
          imagePullPolicy: 'Always'
          ports:
            - containerPort: 1323
          envFrom:
            - configMapRef:
                name: scs-user-config

