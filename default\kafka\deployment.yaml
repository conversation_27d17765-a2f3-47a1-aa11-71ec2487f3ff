apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: kafka
spec:
  serviceName: "kafka-headless"
  replicas: 1
  selector:
    matchLabels:
      app: kafka
  template:
    metadata:
      labels:
        app: kafka
    spec:
      containers:
        - name: kafka
          image: confluentinc/cp-kafka:7.5.0
          ports:
            - containerPort: 9092
          env:
            - name: POD_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
            - name: KAFKA_BROKER_ID
              value: "1"
            - name: KAFKA_ZOOKEEPER_CONNECT
              value: zookeeper:2181
            - name: KAFKA_LISTENERS
              value: PLAINTEXT://:9092
            - name: <PERSON><PERSON><PERSON>_ADVERTISED_LISTENERS
              value: PLAINTEXT://$(POD_IP):9092
            - name: KAFKA_LOG_DIRS
              value: /var/lib/kafka/data
            - name: KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR
              value: "1"
          command: ["/bin/bash", "-c"]
          args:
            - |
              export KAFKA_ADVERTISED_LISTENERS=PLAINTEXT://${POD_IP}:9092
              exec /etc/confluent/docker/run
          volumeMounts:
            - name: kafka-storage
              mountPath: /var/lib/kafka/data
  volumeClaimTemplates:
    - metadata:
        name: kafka-storage
      spec:
        accessModes: ["ReadWriteOnce"]
        resources:
          requests:
            storage: 5Gi
