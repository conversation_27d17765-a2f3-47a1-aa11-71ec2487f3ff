apiVersion: apps/v1
kind: Deployment
metadata:
  name: scs-notification
  annotations:
    configmap.reloader.stakater.com/reload: "scs-notification-config"
spec:
  replicas: 1
  selector:
    matchLabels:
      app: scs-notification
  template:
    metadata:
      labels:
        app: scs-notification
    spec:
      containers:
        - name: scs-notification
          image: erik1303/scss:scs-notification
          imagePullPolicy: 'Always'
          ports:
            - containerPort: 3000
          envFrom:
            - configMapRef:
                name: scs-notification-config

