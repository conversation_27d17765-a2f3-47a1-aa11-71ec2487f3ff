apiVersion: apps/v1
kind: Deployment
metadata:
  name: minio
spec:
  replicas: 1
  selector:
    matchLabels:
      app: minio
  template:
    metadata:
      labels:
        app: minio
    spec:
      containers:
        - name: minio
          image: bitnami/minio:2025.4.22
          ports:
            - containerPort: 9000
            - containerPort: 9001
          env:
            - name: MINIO_ROOT_USER
              value: admin
            - name: MINIO_ROOT_PASSWORD
              value: admin123!
            - name: MINIO_CONSOLE_ADDRESS
              value: ":9001"
            - name: MINIO_API_ADDRESS
              value: ":9000" 
          args:
            - server
            - /bitnami/minio/data
          volumeMounts:
            - name: minio-storage
              mountPath: /bitnami/minio/data
      volumes:
        - name: minio-storage
          persistentVolumeClaim:
            claimName: minio-pvc
