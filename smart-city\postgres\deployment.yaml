apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
    spec:
      containers:
        - name: postgres
          image: postgres:17.2
          ports:
            - containerPort: 5432
          env:
            - name: POSTGRES_DB
              value: smart_city
            - name: POSTGRES_USER
              value: scs_user
            - name: POSTGRES_PASSWORD
              value: scs_user123!
          volumeMounts:
            - name: postgres-storage
              mountPath: /var/lib/postgresql/data
      volumes:
        - name: postgres-storage
          persistentVolumeClaim:
            claimName: postgres-pvc