apiVersion: v1
kind: ConfigMap
metadata:
  name: coursity-be-config
  namespace: coursity
data:
  APP_PORT: "4000"
  APP_VERSION: "1"
  DB_HOST: "postgres"
  DB_DB_PORT: "5432"
  DB_USER: "coursity_user"
  DB_PASSWORD: "coursity_user123!"
  DB_NAME: "coursity"
  IGNORED_ROUTES: "/api/v1/health,/api/v1/files/video/*"
  CLERK_SECRET_KEY: "**************************************************"
  CLERK_API_KEY: "P@ssw0rd"
  CLERK_PUBLISHABLE_KEY: "pk_live_Y2xlcmsuY291cnNpdHkuaW8udm4k"
  MINIO_ENDPOINT: "minio"
  MINIO_ACCESS_KEY: "DOG4TmC9rPmecO9eIm3c"
  MINIO_SECRET_KEY: "vZQKDMVCKOZCamRMvexzoLsilg1YxfIhL9z5DpeE"
  MINIO_BUCKET_NAME: "coursity"
  MINIO_USE_SSL: "false"
  MINIO_PORT: "9000"
